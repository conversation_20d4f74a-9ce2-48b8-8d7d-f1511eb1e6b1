<template>
  <div class="streamer-page">
    <div class="page-container">
      <!-- 主播中心头部 -->
      <div class="streamer-header">
        <h1>主播中心</h1>
        <div class="header-actions">
          <el-button
            v-if="!isLive"
            type="primary"
            size="large"
            @click="handleStartLive"
          >
            开始直播
          </el-button>
          <el-button
            v-else
            type="danger"
            size="large"
            @click="handleStopLive"
          >
            结束直播
          </el-button>
        </div>
      </div>

      <!-- 直播状态卡片 -->
      <div class="status-cards">
        <div class="status-card">
          <div class="card-icon live-icon">
            <el-icon size="24"><VideoCamera /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-value">{{ isLive ? '直播中' : '未开播' }}</div>
            <div class="card-label">直播状态</div>
          </div>
        </div>
        
        <div class="status-card">
          <div class="card-icon viewer-icon">
            <el-icon size="24"><View /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-value">{{ currentViewers }}</div>
            <div class="card-label">当前观众</div>
          </div>
        </div>
        
        <div class="status-card">
          <div class="card-icon revenue-icon">
            <el-icon size="24"><Money /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ todayRevenue }}</div>
            <div class="card-label">今日收入</div>
          </div>
        </div>
        
        <div class="status-card">
          <div class="card-icon follower-icon">
            <el-icon size="24"><User /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-value">{{ totalFollowers }}</div>
            <div class="card-label">总粉丝数</div>
          </div>
        </div>
      </div>

      <!-- 标签页 -->
      <el-tabs v-model="activeTab" class="streamer-tabs">
        <el-tab-pane label="直播间设置" name="room">
          <div class="tab-content">
            <el-form
              ref="roomFormRef"
              :model="roomForm"
              :rules="roomRules"
              label-width="100px"
              class="room-form"
            >
              <el-form-item label="直播间标题" prop="title">
                <el-input
                  v-model="roomForm.title"
                  placeholder="请输入直播间标题"
                  maxlength="50"
                  show-word-limit
                />
              </el-form-item>
              
              <el-form-item label="直播间描述" prop="description">
                <el-input
                  v-model="roomForm.description"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入直播间描述"
                  maxlength="200"
                  show-word-limit
                />
              </el-form-item>
              
              <el-form-item label="直播分类" prop="category_id">
                <el-select v-model="roomForm.category_id" placeholder="请选择分类">
                  <el-option
                    v-for="category in categories"
                    :key="category.id"
                    :label="category.name"
                    :value="category.id"
                  />
                </el-select>
              </el-form-item>
              
              <el-form-item label="封面图片" prop="cover_image">
                <el-upload
                  class="cover-uploader"
                  action="/api/upload"
                  :show-file-list="false"
                  :on-success="handleCoverSuccess"
                  :before-upload="beforeCoverUpload"
                >
                  <el-image
                    v-if="roomForm.cover_image"
                    :src="roomForm.cover_image"
                    style="width: 200px; height: 120px"
                    fit="cover"
                  />
                  <el-icon v-else class="cover-uploader-icon"><Plus /></el-icon>
                </el-upload>
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="handleSaveRoom">
                  保存设置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <el-tab-pane label="推流设置" name="stream">
          <div class="tab-content">
            <div class="stream-info">
              <el-alert
                title="推流信息"
                description="请使用以下信息配置您的推流软件（如OBS）"
                type="info"
                :closable="false"
                show-icon
              />
              
              <div class="stream-config" style="margin-top: 20px">
                <div class="config-item">
                  <label>推流地址:</label>
                  <el-input
                    :value="streamConfig.rtmpUrl"
                    readonly
                  >
                    <template #append>
                      <el-button @click="copyToClipboard(streamConfig.rtmpUrl)">
                        复制
                      </el-button>
                    </template>
                  </el-input>
                </div>
                
                <div class="config-item">
                  <label>推流密钥:</label>
                  <el-input
                    :value="streamConfig.streamKey"
                    readonly
                    type="password"
                    show-password
                  >
                    <template #append>
                      <el-button @click="copyToClipboard(streamConfig.streamKey)">
                        复制
                      </el-button>
                    </template>
                  </el-input>
                </div>
                
                <div class="config-actions">
                  <el-button @click="handleResetStreamKey">
                    重置推流密钥
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="收入统计" name="revenue">
          <div class="tab-content">
            <div class="revenue-stats">
              <div class="stats-header">
                <h3>收入概览</h3>
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="handleDateChange"
                />
              </div>
              
              <div class="revenue-cards">
                <div class="revenue-card">
                  <div class="card-title">总收入</div>
                  <div class="card-amount">¥{{ revenueStats.total }}</div>
                </div>
                <div class="revenue-card">
                  <div class="card-title">礼物收入</div>
                  <div class="card-amount">¥{{ revenueStats.gifts }}</div>
                </div>
                <div class="revenue-card">
                  <div class="card-title">打赏收入</div>
                  <div class="card-amount">¥{{ revenueStats.tips }}</div>
                </div>
              </div>
              
              <div class="revenue-chart">
                <div id="revenueChart" style="height: 300px;"></div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 开始直播对话框 -->
    <el-dialog v-model="liveDialogVisible" title="开始直播" width="500px">
      <div class="live-dialog-content">
        <p>确定要开始直播吗？请确保您已经配置好推流软件并开始推流。</p>
        <el-alert
          title="注意"
          description="开始直播后，您的直播间将对所有用户可见。"
          type="warning"
          :closable="false"
        />
      </div>
      <template #footer>
        <el-button @click="liveDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmStartLive">确定开始</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { liveApi } from '@/services/live'

const userStore = useUserStore()

const activeTab = ref('room')
const isLive = ref(false)
const currentViewers = ref(0)
const todayRevenue = ref(0)
const totalFollowers = ref(0)
const liveDialogVisible = ref(false)

const roomFormRef = ref()
const roomForm = ref({
  title: '',
  description: '',
  category_id: null,
  cover_image: ''
})

const categories = ref([])
const dateRange = ref([])

const streamConfig = ref({
  rtmpUrl: 'rtmp://push.example.com/live/',
  streamKey: 'your_stream_key_here'
})

const revenueStats = ref({
  total: 0,
  gifts: 0,
  tips: 0
})

const roomRules = {
  title: [
    { required: true, message: '请输入直播间标题', trigger: 'blur' }
  ],
  category_id: [
    { required: true, message: '请选择直播分类', trigger: 'change' }
  ]
}

// 获取直播间信息
const getRoomInfo = async () => {
  try {
    // TODO: 调用API获取直播间信息
    roomForm.value = {
      title: '我的直播间',
      description: '欢迎来到我的直播间',
      category_id: 1,
      cover_image: '/images/default-cover.jpg'
    }
  } catch (error) {
    console.error('获取直播间信息失败:', error)
  }
}

// 获取分类列表
const getCategories = async () => {
  try {
    const data = await liveApi.getCategories()
    categories.value = data
  } catch (error) {
    console.error('获取分类列表失败:', error)
  }
}

// 获取统计数据
const getStats = async () => {
  try {
    // TODO: 调用API获取统计数据
    currentViewers.value = 123
    todayRevenue.value = 456.78
    totalFollowers.value = 1234
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 开始直播
const handleStartLive = () => {
  liveDialogVisible.value = true
}

const confirmStartLive = async () => {
  try {
    // TODO: 调用API开始直播
    isLive.value = true
    liveDialogVisible.value = false
    ElMessage.success('直播已开始')
  } catch (error: any) {
    console.error('开始直播失败:', error)
    ElMessage.error(error.message || '开始直播失败')
  }
}

// 结束直播
const handleStopLive = async () => {
  try {
    await ElMessageBox.confirm('确定要结束直播吗？', '确认操作')
    // TODO: 调用API结束直播
    isLive.value = false
    ElMessage.success('直播已结束')
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('结束直播失败:', error)
      ElMessage.error(error.message || '结束直播失败')
    }
  }
}

// 保存直播间设置
const handleSaveRoom = async () => {
  try {
    await roomFormRef.value.validate()
    // TODO: 调用API保存直播间设置
    ElMessage.success('保存成功')
  } catch (error: any) {
    console.error('保存失败:', error)
    ElMessage.error(error.message || '保存失败')
  }
}

// 封面上传成功
const handleCoverSuccess = (response: any) => {
  roomForm.value.cover_image = response.url
  ElMessage.success('封面上传成功')
}

// 封面上传前检查
const beforeCoverUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 复制到剪贴板
const copyToClipboard = (text: string) => {
  navigator.clipboard.writeText(text).then(() => {
    ElMessage.success('已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}

// 重置推流密钥
const handleResetStreamKey = async () => {
  try {
    await ElMessageBox.confirm('重置推流密钥后，需要重新配置推流软件。确定要重置吗？', '确认操作')
    // TODO: 调用API重置推流密钥
    streamConfig.value.streamKey = 'new_stream_key_here'
    ElMessage.success('推流密钥已重置')
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('重置失败:', error)
      ElMessage.error(error.message || '重置失败')
    }
  }
}

// 日期范围变化
const handleDateChange = () => {
  // TODO: 根据日期范围获取收入统计
}

onMounted(() => {
  getRoomInfo()
  getCategories()
  getStats()
})
</script>

<style scoped>
.streamer-page {
  min-height: 100vh;
  background: #f5f7fa;
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.streamer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.streamer-header h1 {
  margin: 0;
  color: #303133;
}

.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.status-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: #fff;
}

.live-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.viewer-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.revenue-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.follower-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #909399;
}

.streamer-tabs {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-content {
  padding: 24px;
}

.room-form {
  max-width: 600px;
}

.cover-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
  width: 200px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cover-uploader:hover {
  border-color: #409eff;
}

.cover-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.stream-config {
  max-width: 600px;
}

.config-item {
  margin-bottom: 20px;
}

.config-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #303133;
}

.config-actions {
  margin-top: 20px;
}

.revenue-stats {
  max-width: 800px;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.stats-header h3 {
  margin: 0;
}

.revenue-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 30px;
}

.revenue-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

.card-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.card-amount {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.live-dialog-content p {
  margin-bottom: 16px;
  color: #606266;
}

@media (max-width: 768px) {
  .page-container {
    padding: 10px;
  }
  
  .streamer-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .status-cards {
    grid-template-columns: 1fr;
  }
  
  .stats-header {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
